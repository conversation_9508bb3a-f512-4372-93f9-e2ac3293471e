import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  ParseIntPipe,
  HttpStatus,
  HttpCode,
  Req,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { StockAdjustmentService } from './stock-adjustment.service';
import { CreateStockAdjustmentDto } from './dto/create-stock-adjustment.dto';
import { UpdateStockAdjustmentDto, ApproveStockAdjustmentDto, CompleteStockAdjustmentDto } from './dto/update-stock-adjustment.dto';
import { StockAdjustment, StockAdjustmentStatus, StockAdjustmentType } from './entities/stock-adjustment.entity';
import { Auth } from '../auth/decorators/auth.decorator';
import { Paginate, PaginateQuery, Paginated } from 'nestjs-paginate';
import { Request } from 'express';

@ApiTags('Stock Adjustment - ใบปรับสต็อก')
@ApiBearerAuth()
@Controller('stock-adjustments')
@Auth()
export class StockAdjustmentController {
  constructor(private readonly stockAdjustmentService: StockAdjustmentService) {}

  @Post()
  @ApiOperation({ 
    summary: 'สร้างใบปรับสต็อกใหม่',
    description: 'สร้างใบปรับสต็อกใหม่พร้อมรายการสินค้าที่ต้องการปรับ'
  })
  @ApiResponse({
    status: 201,
    description: 'สร้างใบปรับสต็อกสำเร็จ',
    type: StockAdjustment,
  })
  @ApiResponse({
    status: 400,
    description: 'ข้อมูลไม่ถูกต้อง',
  })
  @ApiResponse({
    status: 404,
    description: 'ไม่พบสาขาหรือสินค้าคงคลัง',
  })
  async create(
    @Body() createStockAdjustmentDto: CreateStockAdjustmentDto,
    @Req() req: Request,
  ): Promise<StockAdjustment> {
    const user = req.user;

    return this.stockAdjustmentService.create(createStockAdjustmentDto, user);
  }

  @Get()
  @ApiOperation({ 
    summary: 'ดึงรายการใบปรับสต็อกทั้งหมด',
    description: 'ดึงรายการใบปรับสต็อกทั้งหมดพร้อมการแบ่งหน้าและการค้นหา'
  })
  @ApiResponse({
    status: 200,
    description: 'ดึงข้อมูลสำเร็จ',
  })
  @ApiQuery({
    name: 'status',
    required: false,
    enum: StockAdjustmentStatus,
    description: 'กรองตามสถานะ',
  })
  @ApiQuery({
    name: 'type',
    required: false,
    enum: StockAdjustmentType,
    description: 'กรองตามประเภท',
  })
  @ApiQuery({
    name: 'branchId',
    required: false,
    type: Number,
    description: 'กรองตามสาขา',
  })
  async findAll(
    @Paginate() query: PaginateQuery,
    @Req() req: Request,
  ): Promise<Paginated<StockAdjustment>> {
    const user = req.user;

    return this.stockAdjustmentService.datatables(query, user);
  }

  @Get(':id')
  @ApiOperation({ 
    summary: 'ดึงข้อมูลใบปรับสต็อกตาม ID',
    description: 'ดึงข้อมูลใบปรับสต็อกพร้อมรายการสินค้าทั้งหมด'
  })
  @ApiParam({
    name: 'id',
    description: 'รหัสใบปรับสต็อก',
    type: Number,
  })
  @ApiResponse({
    status: 200,
    description: 'ดึงข้อมูลสำเร็จ',
    type: StockAdjustment,
  })
  @ApiResponse({
    status: 404,
    description: 'ไม่พบใบปรับสต็อก',
  })
  async findOne(@Param('id', ParseIntPipe) id: number): Promise<StockAdjustment> {
    return this.stockAdjustmentService.findOne(id);
  }

  @Patch(':id')
  @ApiOperation({ 
    summary: 'แก้ไขใบปรับสต็อก',
    description: 'แก้ไขข้อมูลใบปรับสต็อก (ไม่สามารถแก้ไขได้หากเสร็จสิ้นแล้ว)'
  })
  @ApiParam({
    name: 'id',
    description: 'รหัสใบปรับสต็อก',
    type: Number,
  })
  @ApiResponse({
    status: 200,
    description: 'แก้ไขสำเร็จ',
    type: StockAdjustment,
  })
  @ApiResponse({
    status: 400,
    description: 'ไม่สามารถแก้ไขได้',
  })
  @ApiResponse({
    status: 404,
    description: 'ไม่พบใบปรับสต็อก',
  })
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateStockAdjustmentDto: UpdateStockAdjustmentDto,
    @Req() req: Request,
  ): Promise<StockAdjustment> {
    const user = req.user;

    return this.stockAdjustmentService.update(id, updateStockAdjustmentDto, user);
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ 
    summary: 'ลบใบปรับสต็อก',
    description: 'ลบใบปรับสต็อก (ไม่สามารถลบได้หากเสร็จสิ้นแล้ว)'
  })
  @ApiParam({
    name: 'id',
    description: 'รหัสใบปรับสต็อก',
    type: Number,
  })
  @ApiResponse({
    status: 204,
    description: 'ลบสำเร็จ',
  })
  @ApiResponse({
    status: 400,
    description: 'ไม่สามารถลบได้',
  })
  @ApiResponse({
    status: 404,
    description: 'ไม่พบใบปรับสต็อก',
  })
  async remove(
    @Param('id', ParseIntPipe) id: number,
    @Req() req: Request,
  ): Promise<void> {
    const user = req.user;

    return this.stockAdjustmentService.remove(id, user);
  }

  @Post(':id/approve')
  @ApiOperation({ 
    summary: 'อนุมัติใบปรับสต็อก',
    description: 'อนุมัติใบปรับสต็อกที่มีสถานะรอดำเนินการ'
  })
  @ApiParam({
    name: 'id',
    description: 'รหัสใบปรับสต็อก',
    type: Number,
  })
  @ApiResponse({
    status: 200,
    description: 'อนุมัติสำเร็จ',
    type: StockAdjustment,
  })
  @ApiResponse({
    status: 400,
    description: 'ไม่สามารถอนุมัติได้',
  })
  @ApiResponse({
    status: 404,
    description: 'ไม่พบใบปรับสต็อก',
  })
  async approve(
    @Param('id', ParseIntPipe) id: number,
    @Body() approveDto: ApproveStockAdjustmentDto,
    @Req() req: Request,
  ): Promise<StockAdjustment> {
    const user = req.user;

    return this.stockAdjustmentService.approve(id, approveDto, user);
  }

  @Post(':id/complete')
  @ApiOperation({ 
    summary: 'เสร็จสิ้นใบปรับสต็อก',
    description: 'เสร็จสิ้นใบปรับสต็อกและอัปเดตสต็อกสินค้าอัตโนมัติ'
  })
  @ApiParam({
    name: 'id',
    description: 'รหัสใบปรับสต็อก',
    type: Number,
  })
  @ApiResponse({
    status: 200,
    description: 'เสร็จสิ้นสำเร็จ',
    type: StockAdjustment,
  })
  @ApiResponse({
    status: 400,
    description: 'ไม่สามารถเสร็จสิ้นได้',
  })
  @ApiResponse({
    status: 404,
    description: 'ไม่พบใบปรับสต็อก',
  })
  async complete(
    @Param('id', ParseIntPipe) id: number,
    @Body() completeDto: CompleteStockAdjustmentDto,
    @Req() req: Request,
  ): Promise<StockAdjustment> {
    const user = req.user;

    return this.stockAdjustmentService.complete(id, completeDto, user);
  }

  @Post(':id/cancel')
  @ApiOperation({ 
    summary: 'ยกเลิกใบปรับสต็อก',
    description: 'ยกเลิกใบปรับสต็อก (เฉพาะสถานะร่างหรือรอดำเนินการ)'
  })
  @ApiParam({
    name: 'id',
    description: 'รหัสใบปรับสต็อก',
    type: Number,
  })
  @ApiResponse({
    status: 200,
    description: 'ยกเลิกสำเร็จ',
    type: StockAdjustment,
  })
  @ApiResponse({
    status: 400,
    description: 'ไม่สามารถยกเลิกได้',
  })
  @ApiResponse({
    status: 404,
    description: 'ไม่พบใบปรับสต็อก',
  })
  async cancel(
    @Param('id', ParseIntPipe) id: number,
    @Req() req: Request,
  ): Promise<StockAdjustment> {
    const user = req.user;

    const updateDto: UpdateStockAdjustmentDto = { status: StockAdjustmentStatus.CANCELLED };

    return this.stockAdjustmentService.update(id, updateDto, user);
  }

  @Get(':id/items')
  @ApiOperation({ 
    summary: 'ดึงรายการสินค้าในใบปรับสต็อก',
    description: 'ดึงรายการสินค้าทั้งหมดในใบปรับสต็อก'
  })
  @ApiParam({
    name: 'id',
    description: 'รหัสใบปรับสต็อก',
    type: Number,
  })
  @ApiResponse({
    status: 200,
    description: 'ดึงข้อมูลสำเร็จ',
  })
  @ApiResponse({
    status: 404,
    description: 'ไม่พบใบปรับสต็อก',
  })
  async getAdjustmentItems(@Param('id', ParseIntPipe) id: number) {
    const stockAdjustment = await this.stockAdjustmentService.findOne(id);
    return stockAdjustment.items;
  }

  @Get('reports/summary')
  @ApiOperation({ 
    summary: 'รายงานสรุปการปรับสต็อก',
    description: 'รายงานสรุปการปรับสต็อกตามช่วงเวลา'
  })
  @ApiQuery({
    name: 'startDate',
    required: false,
    type: String,
    description: 'วันที่เริ่มต้น (YYYY-MM-DD)',
  })
  @ApiQuery({
    name: 'endDate',
    required: false,
    type: String,
    description: 'วันที่สิ้นสุด (YYYY-MM-DD)',
  })
  @ApiQuery({
    name: 'branchId',
    required: false,
    type: Number,
    description: 'กรองตามสาขา',
  })
  @ApiResponse({
    status: 200,
    description: 'ดึงข้อมูลสำเร็จ',
  })
  async getAdjustmentSummary(
    @Req() req: Request,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('branchId') branchId?: number,
  ) {
    // TODO: Implement summary report logic
    return {
      message: 'รายงานสรุปการปรับสต็อก - ยังไม่ได้ implement',
      filters: { startDate, endDate, branchId },
    };
  }
}
