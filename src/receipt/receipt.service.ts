import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource, QueryRunner, Like } from 'typeorm';
import { Receipt, ReceiptStatus } from './entities/receipt.entity';
import { ReceiptItem } from './entities/receipt-item.entity';
import { Product } from '../product/entities/product.entity';
import { Branch } from '../branch/entities/branch.entity';
import { Inventory } from '../inventory/entities/inventory.entity';
import { Vendor } from '../vendor/entities/vendor.entity';
import { InventoryTransaction, InventoryTransactionType } from '../inventory/entities/inventory-transaction.entity';
import { CreateReceiptDto } from './dto/create-receipt.dto';
import { UpdateReceiptDto, ApproveReceiptDto, CompleteReceiptDto } from './dto/update-receipt.dto';
import { PaginateQuery, Paginated, paginate, PaginateConfig } from 'nestjs-paginate';

const RECEIPT_PAGINATION_CONFIG: PaginateConfig<Receipt> = {
  sortableColumns: ['id', 'receiptNo', 'receiptDate', 'totalAmount', 'status', 'createdAt'],
  nullSort: 'last',
  defaultSortBy: [['createdAt', 'DESC']],
  searchableColumns: ['receiptNo', 'referenceNo', 'supplier', 'notes'],
  select: [
    'id', 'receiptNo', 'type', 'status', 'receiptDate', 'referenceNo', 'supplier',
    'totalItems', 'totalQuantity', 'totalAmount', 'notes', 'createdAt', 'updatedAt',
    'branch.id', 'branch.name', 'createdBy.id', 'createdBy.firstName', 'createdBy.lastName'
  ],
  relations: ['branch', 'createdBy'],
  defaultLimit: 20,
  maxLimit: 100,
};

@Injectable()
export class ReceiptService {
  constructor(
    @InjectRepository(Receipt)
    private receiptRepository: Repository<Receipt>,
    @InjectRepository(ReceiptItem)
    private receiptItemRepository: Repository<ReceiptItem>,
    @InjectRepository(Product)
    private productRepository: Repository<Product>,
    @InjectRepository(Branch)
    private branchRepository: Repository<Branch>,
    @InjectRepository(Inventory)
    private inventoryRepository: Repository<Inventory>,
    @InjectRepository(InventoryTransaction)
    private transactionRepository: Repository<InventoryTransaction>,
    @InjectRepository(Vendor)
    private vendorRepository: Repository<Vendor>,
    private dataSource: DataSource,
  ) {}

  async datatables(query: PaginateQuery, user: any): Promise<Paginated<Receipt>> {
    return paginate(query, this.receiptRepository, {
      ...RECEIPT_PAGINATION_CONFIG,
      where: {
        ...(user?.storeId ? { store: { id: user.storeId } } : {}),
      },
    });
  }

  async create(createReceiptDto: CreateReceiptDto, user: any): Promise<Receipt> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // ตรวจสอบสาขา
      const branch = await this.branchRepository.findOne({
        where: {
          id: createReceiptDto.branchId,
          ...(user?.storeId ? { store: { id: user.storeId } } : {}),
        },
      });

      if (!branch) {
        throw new NotFoundException('ไม่พบสาขา');
      }

      // สร้างหมายเลขใบรับของ
      const receiptNo = await this.generateReceiptNo(createReceiptDto.type);

      // คำนวณยอดรวม
      let totalItems = 0;
      let totalQuantity = 0;
      let subtotal = 0;

      for (const item of createReceiptDto.items) {
        // ตรวจสอบสินค้า
        const product = await this.productRepository.findOne({
          where: {
            id: item.productId,
            ...(user?.storeId ? { store: { id: user.storeId } } : {}),
          },
        });

        if (!product) {
          throw new NotFoundException(`ไม่พบสินค้า ID: ${item.productId}`);
        }

        totalItems++;
        totalQuantity += item.quantity;
        
        const itemTotal = (item.quantity * (item.unitPrice || item.unitCost || 0)) - (item.discountAmount || 0) + (item.taxAmount || 0);
        subtotal += itemTotal;
      }

      const totalAmount = subtotal - (createReceiptDto.discountAmount || 0) + (createReceiptDto.taxAmount || 0);

      // ตรวจสอบ vendor หากมีการระบุ
      let vendor = null;
      if (createReceiptDto.vendorId) {
        vendor = await this.vendorRepository.findOne({
          where: { id: createReceiptDto.vendorId },
        });
        if (!vendor) {
          throw new NotFoundException('ไม่พบผู้จำหน่าย');
        }
      }

      // สร้างใบรับของ
      const receipt = this.receiptRepository.create({
        receiptNo,
        type: createReceiptDto.type,
        status: createReceiptDto.status,
        receiptDate: new Date(createReceiptDto.receiptDate),
        referenceNo: createReceiptDto.referenceNo,
        vendor: vendor ? { id: vendor.id } : null,
        supplier: createReceiptDto.supplier,
        supplierAddress: createReceiptDto.supplierAddress,
        supplierPhone: createReceiptDto.supplierPhone,
        totalItems,
        totalQuantity,
        subtotal,
        taxAmount: createReceiptDto.taxAmount || 0,
        discountAmount: createReceiptDto.discountAmount || 0,
        totalAmount,
        notes: createReceiptDto.notes,
        branch: { id: createReceiptDto.branchId },
        store: { id: user?.storeId },
        createdBy: { id: user?.id },
      });

      const savedReceipt = await queryRunner.manager.save(receipt);

      // สร้างรายการสินค้า
      for (const itemDto of createReceiptDto.items) {
        const itemTotal = (itemDto.quantity * (itemDto.unitPrice || itemDto.unitCost || 0)) - (itemDto.discountAmount || 0) + (itemDto.taxAmount || 0);

        const receiptItem = this.receiptItemRepository.create({
          quantity: itemDto.quantity,
          unitCost: itemDto.unitCost,
          unitPrice: itemDto.unitPrice,
          discountAmount: itemDto.discountAmount || 0,
          taxAmount: itemDto.taxAmount || 0,
          totalAmount: itemTotal,
          batchNo: itemDto.batchNo,
          expiryDate: itemDto.expiryDate ? new Date(itemDto.expiryDate) : null,
          notes: itemDto.notes,
          receipt: { id: savedReceipt.id },
          product: { id: itemDto.productId },
          inventory: itemDto.inventoryId ? { id: itemDto.inventoryId } : null,
        });

        await queryRunner.manager.save(receiptItem);
      }

      await queryRunner.commitTransaction();
      return this.findOne(savedReceipt.id);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async findAll(user: any, filters?: any): Promise<Receipt[]> {
    const where: any = {
      ...(user?.storeId ? { store: { id: user.storeId } } : {}),
    };

    if (filters?.status) {
      where.status = filters.status;
    }

    if (filters?.type) {
      where.type = filters.type;
    }

    if (filters?.branchId) {
      where.branch = { id: filters.branchId };
    }

    return this.receiptRepository.find({
      where,
      relations: ['branch', 'store', 'createdBy', 'vendor', 'items', 'items.product'],
      order: { createdAt: 'DESC' },
    });
  }

  async findOne(id: number): Promise<Receipt> {
    const receipt = await this.receiptRepository.findOne({
      where: { id },
      relations: [
        'branch', 'store', 'createdBy', 'approvedBy', 'vendor',
        'items', 'items.product', 'items.inventory'
      ],
    });

    if (!receipt) {
      throw new NotFoundException('ไม่พบใบรับของ');
    }

    return receipt;
  }

  async update(id: number, updateReceiptDto: UpdateReceiptDto, user: any): Promise<Receipt> {
    const receipt = await this.findOne(id);

    // ตรวจสอบสิทธิ์การแก้ไข
    if (receipt.status === ReceiptStatus.COMPLETED) {
      throw new BadRequestException('ไม่สามารถแก้ไขใบรับของที่เสร็จสิ้นแล้ว');
    }

    if (user?.storeId && receipt.store.id !== user.storeId) {
      throw new BadRequestException('ไม่มีสิทธิ์แก้ไขใบรับของนี้');
    }

    // อัปเดตข้อมูล
    Object.assign(receipt, updateReceiptDto);

    if (updateReceiptDto.receiptDate) {
      receipt.receiptDate = new Date(updateReceiptDto.receiptDate);
    }

    await this.receiptRepository.save(receipt);
    return this.findOne(id);
  }

  async remove(id: number, user: any): Promise<void> {
    const receipt = await this.findOne(id);

    // ตรวจสอบสิทธิ์การลบ
    if (receipt.status === ReceiptStatus.COMPLETED) {
      throw new BadRequestException('ไม่สามารถลบใบรับของที่เสร็จสิ้นแล้ว');
    }

    if (user?.storeId && receipt.store.id !== user.storeId) {
      throw new BadRequestException('ไม่มีสิทธิ์ลบใบรับของนี้');
    }

    await this.receiptRepository.softDelete(id);
  }

  async approve(id: number, approveDto: ApproveReceiptDto, user: any): Promise<Receipt> {
    const receipt = await this.findOne(id);

    if (receipt.status !== ReceiptStatus.PENDING) {
      throw new BadRequestException('สามารถอนุมัติได้เฉพาะใบรับของที่มีสถานะรอดำเนินการเท่านั้น');
    }

    receipt.status = ReceiptStatus.APPROVED;
    receipt.approvedAt = new Date();
    receipt.approvedBy = { id: user?.id } as any;

    if (approveDto.approvalNotes) {
      receipt.notes = receipt.notes ? `${receipt.notes}\n\nการอนุมัติ: ${approveDto.approvalNotes}` : `การอนุมัติ: ${approveDto.approvalNotes}`;
    }

    await this.receiptRepository.save(receipt);
    return this.findOne(id);
  }

  async complete(id: number, completeDto: CompleteReceiptDto, user: any): Promise<Receipt> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const receipt = await this.findOne(id);

      if (receipt.status !== ReceiptStatus.APPROVED) {
        throw new BadRequestException('สามารถเสร็จสิ้นได้เฉพาะใบรับของที่อนุมัติแล้วเท่านั้น');
      }

      // อัปเดตสต็อกสินค้า
      for (const item of receipt.items) {
        await this.updateInventoryFromReceipt(item, queryRunner);
      }

      // อัปเดตสถานะใบรับของ
      receipt.status = ReceiptStatus.COMPLETED;
      receipt.completedAt = new Date();

      if (completeDto.completionNotes) {
        receipt.notes = receipt.notes ? `${receipt.notes}\n\nการเสร็จสิ้น: ${completeDto.completionNotes}` : `การเสร็จสิ้น: ${completeDto.completionNotes}`;
      }

      await queryRunner.manager.save(receipt);
      await queryRunner.commitTransaction();

      return this.findOne(id);
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  private async updateInventoryFromReceipt(receiptItem: ReceiptItem, queryRunner: QueryRunner): Promise<void> {
    // ค้นหาหรือสร้าง inventory record
    let inventory = await queryRunner.manager.findOne(Inventory, {
      where: {
        product: { id: receiptItem.product.id },
        branch: { id: receiptItem.receipt.branch.id },
      },
    });

    const stockBefore = inventory?.currentStock || 0;

    if (!inventory) {
      // สร้าง inventory ใหม่
      inventory = queryRunner.manager.create(Inventory, {
        product: { id: receiptItem.product.id },
        branch: { id: receiptItem.receipt.branch.id },
        store: { id: receiptItem.receipt.store.id },
        currentStock: receiptItem.quantity,
        availableStock: receiptItem.quantity,
        reservedStock: 0,
        averageCost: receiptItem.unitCost || 0,
        lastCost: receiptItem.unitCost || 0,
        lastUpdated: new Date(),
        active: true,
      });
    } else {
      // อัปเดต inventory ที่มีอยู่
      inventory.currentStock += receiptItem.quantity;
      inventory.availableStock += receiptItem.quantity;
      
      if (receiptItem.unitCost) {
        // คำนวณ weighted average cost
        const totalValue = (stockBefore * inventory.averageCost) + (receiptItem.quantity * receiptItem.unitCost);
        const totalQuantity = stockBefore + receiptItem.quantity;
        inventory.averageCost = totalQuantity > 0 ? totalValue / totalQuantity : receiptItem.unitCost;
        inventory.lastCost = receiptItem.unitCost;
      }
      
      inventory.lastUpdated = new Date();
    }

    const savedInventory = await queryRunner.manager.save(inventory);

    // อัปเดต receiptItem ด้วยข้อมูล stock
    receiptItem.stockBefore = stockBefore;
    receiptItem.stockAfter = savedInventory.currentStock;
    receiptItem.inventory = savedInventory;
    await queryRunner.manager.save(receiptItem);

    // สร้าง inventory transaction
    const transactionNo = `RCP-${receiptItem.receipt.receiptNo}-${receiptItem.product.id}`;
    const transaction = queryRunner.manager.create(InventoryTransaction, {
      transactionNo,
      type: InventoryTransactionType.STOCK_IN,
      quantity: receiptItem.quantity,
      unitCost: receiptItem.unitCost,
      totalCost: receiptItem.quantity * (receiptItem.unitCost || 0),
      stockBefore,
      stockAfter: savedInventory.currentStock,
      referenceNo: receiptItem.receipt.receiptNo,
      notes: `รับสินค้าจากใบรับของ ${receiptItem.receipt.receiptNo}`,
      supplier: receiptItem.receipt.supplier,
      batchNo: receiptItem.batchNo,
      expiryDate: receiptItem.expiryDate,
      inventory: savedInventory,
      createdBy: receiptItem.receipt.createdBy,
    });

    await queryRunner.manager.save(transaction);
  }

  /**
   * ดูประวัติการซื้อจาก vendor
   * @param vendorId รหัสผู้จำหน่าย
   * @param user ผู้ใช้งาน
   * @param filters ตัวกรองเพิ่มเติม
   */
  async getVendorPurchaseHistory(vendorId: number, user: any, filters?: any): Promise<Receipt[]> {
    const where: any = {
      vendor: { id: vendorId },
      ...(user?.storeId ? { store: { id: user.storeId } } : {}),
    };

    if (filters?.status) {
      where.status = filters.status;
    }

    if (filters?.dateFrom) {
      where.receiptDate = { ...where.receiptDate, gte: new Date(filters.dateFrom) };
    }

    if (filters?.dateTo) {
      where.receiptDate = { ...where.receiptDate, lte: new Date(filters.dateTo) };
    }

    return this.receiptRepository.find({
      where,
      relations: ['branch', 'store', 'createdBy', 'vendor', 'items', 'items.product'],
      order: { receiptDate: 'DESC', createdAt: 'DESC' },
    });
  }

  /**
   * สรุปยอดซื้อจาก vendor
   * @param vendorId รหัสผู้จำหน่าย
   * @param user ผู้ใช้งาน
   * @param filters ตัวกรองเพิ่มเติม
   */
  async getVendorPurchaseSummary(vendorId: number, user: any, filters?: any): Promise<any> {
    const where: any = {
      vendor: { id: vendorId },
      status: 'completed', // เฉพาะใบรับของที่เสร็จสิ้นแล้ว
      ...(user?.storeId ? { store: { id: user.storeId } } : {}),
    };

    if (filters?.dateFrom) {
      where.receiptDate = { ...where.receiptDate, gte: new Date(filters.dateFrom) };
    }

    if (filters?.dateTo) {
      where.receiptDate = { ...where.receiptDate, lte: new Date(filters.dateTo) };
    }

    const receipts = await this.receiptRepository.find({
      where,
      relations: ['items'],
    });

    const summary = {
      totalReceipts: receipts.length,
      totalAmount: 0,
      totalQuantity: 0,
      totalItems: 0,
      averageOrderValue: 0,
      lastPurchaseDate: null as Date | null,
      firstPurchaseDate: null as Date | null,
    };

    if (receipts.length > 0) {
      summary.totalAmount = receipts.reduce((sum, receipt) => sum + receipt.totalAmount, 0);
      summary.totalQuantity = receipts.reduce((sum, receipt) => sum + receipt.totalQuantity, 0);
      summary.totalItems = receipts.reduce((sum, receipt) => sum + receipt.totalItems, 0);
      summary.averageOrderValue = summary.totalAmount / receipts.length;

      const dates = receipts.map(r => r.receiptDate).sort();
      summary.firstPurchaseDate = dates[0];
      summary.lastPurchaseDate = dates[dates.length - 1];
    }

    return summary;
  }

  private async generateReceiptNo(type: string): Promise<string> {
    const prefix = 'RCP';
    const year = new Date().getFullYear().toString().slice(-2);
    const month = (new Date().getMonth() + 1).toString().padStart(2, '0');
    
    const lastReceipt = await this.receiptRepository.findOne({
      where: { receiptNo: Like(`${prefix}-${year}${month}%`) },
      order: { receiptNo: 'DESC' },
    });

    let sequence = 1;
    if (lastReceipt) {
      const lastSequence = parseInt(lastReceipt.receiptNo.split('-')[1].slice(4));
      sequence = lastSequence + 1;
    }

    return `${prefix}-${year}${month}${sequence.toString().padStart(4, '0')}`;
  }
}
