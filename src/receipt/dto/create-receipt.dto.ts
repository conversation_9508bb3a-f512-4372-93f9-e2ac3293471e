import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsOptional, IsString, IsEnum, IsDateString, IsArray, ValidateNested, Min } from 'class-validator';
import { Type } from 'class-transformer';
import { ReceiptType, ReceiptStatus } from '../entities/receipt.entity';

export class CreateReceiptItemDto {
  @ApiProperty({ description: 'รหัสสินค้า' })
  @IsNotEmpty({ message: 'กรุณาระบุรหัสสินค้า' })
  @IsNumber({}, { message: 'รหัสสินค้าต้องเป็นตัวเลข' })
  readonly productId: number;

  @ApiProperty({ description: 'รหัสสินค้าคงคลัง', required: false })
  @IsOptional()
  @IsNumber({}, { message: 'รหัสสินค้าคงคลังต้องเป็นตัวเลข' })
  readonly inventoryId?: number;

  @ApiProperty({ description: 'จำนวนที่รับ' })
  @IsNotEmpty({ message: 'กรุณาระบุจำนวน' })
  @IsNumber({}, { message: 'จำนวนต้องเป็นตัวเลข' })
  @Min(0.01, { message: 'จำนวนต้องมากกว่า 0' })
  readonly quantity: number;

  @ApiProperty({ description: 'ต้นทุนต่อหน่วย', required: false })
  @IsOptional()
  @IsNumber({}, { message: 'ต้นทุนต่อหน่วยต้องเป็นตัวเลข' })
  @Min(0, { message: 'ต้นทุนต่อหน่วยต้องไม่ติดลบ' })
  readonly unitCost?: number;

  @ApiProperty({ description: 'ราคาต่อหน่วย', required: false })
  @IsOptional()
  @IsNumber({}, { message: 'ราคาต่อหน่วยต้องเป็นตัวเลข' })
  @Min(0, { message: 'ราคาต่อหน่วยต้องไม่ติดลบ' })
  readonly unitPrice?: number;

  @ApiProperty({ description: 'ส่วนลดต่อรายการ', required: false, default: 0 })
  @IsOptional()
  @IsNumber({}, { message: 'ส่วนลดต้องเป็นตัวเลข' })
  @Min(0, { message: 'ส่วนลดต้องไม่ติดลบ' })
  readonly discountAmount?: number = 0;

  @ApiProperty({ description: 'จำนวนภาษีต่อรายการ', required: false, default: 0 })
  @IsOptional()
  @IsNumber({}, { message: 'ภาษีต้องเป็นตัวเลข' })
  @Min(0, { message: 'ภาษีต้องไม่ติดลบ' })
  readonly taxAmount?: number = 0;

  @ApiProperty({ description: 'หมายเลขแบทช์หรือล็อต', required: false })
  @IsOptional()
  @IsString({ message: 'หมายเลขแบทช์ต้องเป็นข้อความ' })
  readonly batchNo?: string;

  @ApiProperty({ description: 'วันหมดอายุ', required: false })
  @IsOptional()
  @IsDateString({}, { message: 'วันหมดอายุต้องเป็นรูปแบบวันที่ที่ถูกต้อง' })
  readonly expiryDate?: string;

  @ApiProperty({ description: 'หมายเหตุสำหรับรายการนี้', required: false })
  @IsOptional()
  @IsString({ message: 'หมายเหตุต้องเป็นข้อความ' })
  readonly notes?: string;
}

export class CreateReceiptDto {
  @ApiProperty({ description: 'รหัสสาขา' })
  @IsNotEmpty({ message: 'กรุณาระบุรหัสสาขา' })
  @IsNumber({}, { message: 'รหัสสาขาต้องเป็นตัวเลข' })
  readonly branchId: number;

  @ApiProperty({
    description: 'ประเภทของใบรับของ',
    enum: ReceiptType
  })
  @IsNotEmpty({ message: 'กรุณาระบุประเภทใบรับของ' })
  @IsEnum(ReceiptType, { message: 'ประเภทใบรับของไม่ถูกต้อง' })
  readonly type: ReceiptType;

  @ApiProperty({
    description: 'สถานะของใบรับของ',
    enum: ReceiptStatus,
    default: ReceiptStatus.DRAFT,
    required: false
  })
  @IsOptional()
  @IsEnum(ReceiptStatus, { message: 'สถานะใบรับของไม่ถูกต้อง' })
  readonly status?: ReceiptStatus = ReceiptStatus.DRAFT;

  @ApiProperty({ description: 'วันที่ของใบรับของ' })
  @IsNotEmpty({ message: 'กรุณาระบุวันที่' })
  @IsDateString({}, { message: 'วันที่ต้องเป็นรูปแบบวันที่ที่ถูกต้อง' })
  readonly receiptDate: string;

  @ApiProperty({ description: 'หมายเลขอ้างอิงเอกสารภายนอก', required: false })
  @IsOptional()
  @IsString({ message: 'หมายเลขอ้างอิงต้องเป็นข้อความ' })
  readonly referenceNo?: string;

  @ApiPropertyOptional({
    description: 'รหัสผู้จำหน่าย (Vendor ID)',
    example: 1
  })
  @IsOptional()
  @IsNumber()
  readonly vendorId?: number;

  @ApiProperty({ description: 'ข้อมูลผู้จำหน่าย', required: false })
  @IsOptional()
  @IsString({ message: 'ข้อมูลผู้จำหน่ายต้องเป็นข้อความ' })
  readonly supplier?: string;

  @ApiProperty({ description: 'ที่อยู่ผู้จำหน่าย', required: false })
  @IsOptional()
  @IsString({ message: 'ที่อยู่ผู้จำหน่ายต้องเป็นข้อความ' })
  readonly supplierAddress?: string;

  @ApiProperty({ description: 'เบอร์โทรศัพท์ผู้จำหน่าย', required: false })
  @IsOptional()
  @IsString({ message: 'เบอร์โทรศัพท์ต้องเป็นข้อความ' })
  readonly supplierPhone?: string;

  @ApiProperty({ description: 'ส่วนลดรวม', required: false, default: 0 })
  @IsOptional()
  @IsNumber({}, { message: 'ส่วนลดต้องเป็นตัวเลข' })
  @Min(0, { message: 'ส่วนลดต้องไม่ติดลบ' })
  readonly discountAmount?: number = 0;

  @ApiProperty({ description: 'ภาษีรวม', required: false, default: 0 })
  @IsOptional()
  @IsNumber({}, { message: 'ภาษีต้องเป็นตัวเลข' })
  @Min(0, { message: 'ภาษีต้องไม่ติดลบ' })
  readonly taxAmount?: number = 0;

  @ApiProperty({ description: 'หมายเหตุเพิ่มเติม', required: false })
  @IsOptional()
  @IsString({ message: 'หมายเหตุต้องเป็นข้อความ' })
  readonly notes?: string;

  @ApiProperty({ 
    description: 'รายการสินค้าในใบรับของ',
    type: [CreateReceiptItemDto]
  })
  @IsNotEmpty({ message: 'กรุณาระบุรายการสินค้า' })
  @IsArray({ message: 'รายการสินค้าต้องเป็น array' })
  @ValidateNested({ each: true })
  @Type(() => CreateReceiptItemDto)
  readonly items: CreateReceiptItemDto[];
}
